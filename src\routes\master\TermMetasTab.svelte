<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { TermMeta, Term } from '$lib/types/tables.js';

  interface Props {
    termMetas?: TermMeta[];
    terms?: Term[];
    token?: string;
    onreload?: () => void;
  }

  let { termMetas = [], terms = [], token = '', onreload = () => {} }: Props = $props();

  // API helper function
  async function apiCall(endpoint: string, data?: any) {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }



  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = $state(1);
  let searchQuery = $state('');
  let metaKeyFilter = $state('');
  let totalTermMetas = $state(0);
  let displayedTermMetas = $state<TermMeta[]>([]);
  let loading = $state(false);

  // Form states
  let showAddDialog = $state(false);
  let showEditDialog = $state(false);

  // Form data
  let newTermMeta = $state<Partial<TermMeta>>({
    term_id: undefined,
    term_meta_key: '',
    term_meta_value: ''
  });
  let editTermMeta = $state<Partial<TermMeta>>({});

  // Server-side pagination functions
  async function loadTermMetas() {
    try {
      loading = true;
      const offset = (currentPage - 1) * itemsPerPage;

      console.log(`Loading term metas: page ${currentPage}, limit ${itemsPerPage}, offset ${offset}, search: "${searchQuery}", meta_key: "${metaKeyFilter}"`);

      const requestData: any = {
        limit: itemsPerPage,
        offset: offset
      };

      // Add search parameter if query is long enough
      if (searchQuery && searchQuery.trim().length >= 3) {
        requestData.search = searchQuery.trim();
      }

      // Add meta key filter if specified
      if (metaKeyFilter && metaKeyFilter.trim().length > 0) {
        requestData.meta_key = metaKeyFilter.trim();
      }

      const response = await apiCall('/api/GetTermMetas', requestData);

      console.log('TermMetas API response:', response);

      if (response.success && response.data) {
        displayedTermMetas = response.data.term_metas || [];
        totalTermMetas = response.data.total || 0;
        console.log(`Loaded ${displayedTermMetas.length} term metas, total: ${totalTermMetas}`);
      } else {
        displayedTermMetas = [];
        totalTermMetas = 0;
        console.error('Failed to load term metas:', response);
      }
    } catch (error) {
      console.error('Error loading term metas:', error);
      displayedTermMetas = [];
      totalTermMetas = 0;
    } finally {
      loading = false;
    }
  }

  function getTotalPages() {
    return Math.ceil(totalTermMetas / itemsPerPage);
  }

  async function changePage(page: number) {
    if (page >= 1 && page <= getTotalPages()) {
      currentPage = page;
      await loadTermMetas();
    }
  }

  // Reload data function
  async function reloadData() {
    await loadTermMetas();
    onreload(); // Also trigger parent reload for tab counts
  }

  // Search and filter change handlers
  async function handleSearchChange() {
    currentPage = 1; // Reset to first page
    await loadTermMetas();
  }

  async function handleMetaKeyFilterChange() {
    currentPage = 1; // Reset to first page
    await loadTermMetas();
  }

  function openEditTermMeta(termMeta: TermMeta) {
    editTermMeta = { ...termMeta };
    showEditDialog = true;
  }

  async function addTermMeta() {
    try {
      await apiCall('/api/SaveTermMeta', { term_meta: newTermMeta });
      newTermMeta = {
        term_id: undefined,
        term_meta_key: '',
        term_meta_value: ''
      };
      showAddDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to add term meta:', error);
      alert('Failed to add term meta');
    }
  }

  async function updateTermMeta() {
    try {
      await apiCall('/api/SaveTermMeta', { term_meta: editTermMeta });
      showEditDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to update term meta:', error);
      alert('Failed to update term meta');
    }
  }

  async function deleteTermMeta(id: number) {
    if (confirm('Are you sure you want to delete this term meta?')) {
      try {
        await apiCall('/api/DeleteTermMeta', { term_meta_id: id });
        reloadData();
      } catch (error) {
        console.error('Failed to delete term meta:', error);
        alert('Failed to delete term meta');
      }
    }
  }

  // Reactive statements
  let totalPages = $derived(getTotalPages());

  // Initialization and debounced handlers
  let searchTimeout: NodeJS.Timeout;
  let filterTimeout: NodeJS.Timeout;
  let hasLoaded = $state(false);

  // Single initialization effect
  $effect(() => {
    if (token && termMetas && termMetas.length > 0 && !hasLoaded) {
      hasLoaded = true;
      loadTermMetas();
    }
  });

  // Search handler with debouncing
  function handleSearchInput() {
    if (searchTimeout) clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      if (searchQuery.length === 0 || searchQuery.length >= 3) {
        handleSearchChange();
      }
    }, 500);
  }

  // Filter handler with debouncing
  function handleMetaKeyFilterInput() {
    if (filterTimeout) clearTimeout(filterTimeout);
    filterTimeout = setTimeout(() => {
      handleMetaKeyFilterChange();
    }, 100);
  }
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Term Metas Management</h2>
    <button
      onclick={() => showAddDialog = true}
      disabled={!terms || terms.length === 0}
      title={!terms || terms.length === 0 ? "Create a term first before adding term metas" : ""}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
    >
      Add New Term Meta
    </button>
  </div>

  <!-- Search and Filter Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search term metas by key or value (min 3 characters)..."
        bind:value={searchQuery}
        oninput={handleSearchInput}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="flex gap-2 items-center">
      <label for="term_meta_key_filter" class="text-sm font-medium text-gray-700">Meta Key:</label>
      <input
        id="term_meta_key_filter"
        type="text"
        placeholder="Filter by key..."
        bind:value={metaKeyFilter}
        oninput={handleMetaKeyFilterInput}
        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-40"
      />
    </div>
    <div class="text-sm text-gray-600">
      {loading ? 'Loading...' : `${displayedTermMetas?.length || 0} of ${totalTermMetas || 0} term metas`}
    </div>
  </div>

  {#if loading}
    <div class="flex justify-center items-center py-8">
      <div class="text-gray-500">Loading term metas...</div>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {#each displayedTermMetas as termMeta}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{termMeta.term_meta_key}</h3>
                <p class="text-sm text-gray-600 break-all">{termMeta.term_meta_value || 'No value'}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {termMeta.term_meta_id}
              </span>
            </div>

            <div class="space-y-1">
              <p class="text-xs text-gray-500">Term ID: {termMeta.term_id}</p>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditTermMeta(termMeta)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteTermMeta(termMeta.term_meta_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

    <!-- Pagination -->
    {#if totalPages > 1}
      <div class="flex justify-center items-center gap-4 mt-6">
        <button
          onclick={() => changePage(currentPage - 1)}
          disabled={currentPage === 1}
          class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Previous
        </button>

        <span class="text-gray-600">
          Page {currentPage} of {totalPages}
        </span>

        <button
          onclick={() => changePage(currentPage + 1)}
          disabled={currentPage === totalPages}
          class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Next
        </button>
      </div>
    {/if}
  {/if}
</div>

<!-- Add Term Meta Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New Term Meta</Dialog.Title>
        <Dialog.Description>Create a new term meta.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="term_meta_term_id" class="text-right text-sm font-medium">Term</label>
          <select
            id="term_meta_term_id"
            bind:value={newTermMeta.term_id}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select a term (required)</option>
            {#each terms as term}
              <option value={term.term_id}>{term.term_name} ({term.term_taxonomy}) - ID: {term.term_id}</option>
            {/each}
          </select>
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="term_meta_key" class="text-right text-sm font-medium">Key</label>
          <input
            id="term_meta_key"
            type="text"
            bind:value={newTermMeta.term_meta_key}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="meta_key"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="term_meta_value" class="text-right text-sm font-medium">Value</label>
          <input
            id="term_meta_value"
            type="text"
            bind:value={newTermMeta.term_meta_value}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Meta value"
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addTermMeta}
          disabled={!newTermMeta.term_meta_key || !newTermMeta.term_id}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add Term Meta
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit Term Meta Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit Term Meta</Dialog.Title>
        <Dialog.Description>Update term meta information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_term_meta_term_id" class="text-right text-sm font-medium">Term</label>
          <select
            id="edit_term_meta_term_id"
            bind:value={editTermMeta.term_id}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select a term (required)</option>
            {#each terms as term}
              <option value={term.term_id}>{term.term_name} ({term.term_taxonomy}) - ID: {term.term_id}</option>
            {/each}
          </select>
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_term_meta_key" class="text-right text-sm font-medium">Key</label>
          <input
            id="edit_term_meta_key"
            type="text"
            bind:value={editTermMeta.term_meta_key}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="meta_key"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_term_meta_value" class="text-right text-sm font-medium">Value</label>
          <input
            id="edit_term_meta_value"
            type="text"
            bind:value={editTermMeta.term_meta_value}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Meta value"
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateTermMeta}
          disabled={!editTermMeta.term_meta_key || !editTermMeta.term_id}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update Term Meta
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
