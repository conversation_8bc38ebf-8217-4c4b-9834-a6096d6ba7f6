import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { getItemMetas, searchItemMetas, getItemMetasByKey } from '$lib/services/item_metas.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const item_id = body.item_id;
    const limit = body.limit || 50;
    const offset = body.offset || 0;
    const search = body.search || '';
    const meta_key = body.meta_key || '';

    let result;

    // If there's a search query, use search function
    if (search && search.trim().length >= 3) {
      result = await searchItemMetas(search.trim(), limit, offset);
    }
    // If there's a meta key filter, use meta key function
    else if (meta_key && meta_key.trim().length > 0) {
      result = await getItemMetasByKey(meta_key.trim(), limit, offset);
    }
    // Otherwise, use regular getItemMetas
    else {
      result = await getItemMetas(item_id, limit, offset);
    }

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
