<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { Item, User } from '$lib/types/tables.js';

  interface Props {
    items?: Item[];
    users?: User[];
    token?: string;
    onreload?: () => void;
  }

  let { items = [], users = [], token = '', onreload = () => {} }: Props = $props();

  // API helper function
  async function apiCall(endpoint: string, data?: any) {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = $state(1);
  let searchQuery = $state('');
  let statusFilter = $state('');
  let totalItems = $state(0);
  let displayedItems = $state<Item[]>([]);
  let loading = $state(false);

  // Form states
  let showAddDialog = $state(false);
  let showEditDialog = $state(false);

  // Form data
  let newItem = $state<Partial<Item>>({
    item_name: '',
    item_slug: '',
    item_url: '',
    item_status: 0,
    user_id: undefined
  });
  let editItem = $state<Partial<Item>>({});

  // Server-side pagination and search functions
  async function loadItems() {
    try {
      loading = true;
      const offset = (currentPage - 1) * itemsPerPage;

      console.log(`Loading items: page ${currentPage}, limit ${itemsPerPage}, offset ${offset}, search: "${searchQuery}", status: "${statusFilter}"`);

      const requestData: any = {
        limit: itemsPerPage,
        offset: offset
      };

      // Add search parameter if query is long enough
      if (searchQuery && searchQuery.trim().length >= 3) {
        requestData.search = searchQuery.trim();
      }

      // Add status filter if selected
      if (statusFilter !== '') {
        requestData.status = statusFilter;
      }

      const response = await apiCall('/api/GetItems', requestData);

      console.log('Items API response:', response);

      if (response.success && response.data) {
        displayedItems = response.data.items || [];
        totalItems = response.data.total || 0;
        console.log(`Loaded ${displayedItems.length} items, total: ${totalItems}`);
      } else {
        displayedItems = [];
        totalItems = 0;
        console.error('Failed to load items:', response);
      }
    } catch (error) {
      console.error('Error loading items:', error);
      displayedItems = [];
      totalItems = 0;
    } finally {
      loading = false;
    }
  }

  function getTotalPages() {
    return Math.ceil(totalItems / itemsPerPage);
  }

  async function changePage(page: number) {
    if (page >= 1 && page <= getTotalPages()) {
      currentPage = page;
      await loadItems();
    }
  }

  // Reload data function
  async function reloadData() {
    await loadItems();
    onreload(); // Also trigger parent reload for tab counts
  }

  // Search and filter change handlers
  async function handleSearchChange() {
    currentPage = 1; // Reset to first page
    await loadItems();
  }

  async function handleStatusFilterChange() {
    currentPage = 1; // Reset to first page
    await loadItems();
  }

  function openEditItem(item: Item) {
    editItem = { ...item };
    showEditDialog = true;
  }

  async function addItem() {
    try {
      await apiCall('/api/SaveItem', { item: newItem });
      newItem = {
        item_name: '',
        item_slug: '',
        item_url: '',
        item_status: 0,
        user_id: undefined
      };
      showAddDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to add item:', error);
      alert('Failed to add item');
    }
  }

  async function updateItem() {
    try {
      await apiCall('/api/SaveItem', { item: editItem });
      showEditDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to update item:', error);
      alert('Failed to update item');
    }
  }

  async function deleteItem(id: number) {
    if (confirm('Are you sure you want to delete this item?')) {
      try {
        await apiCall('/api/DeleteItem', { item_id: id });
        reloadData();
      } catch (error) {
        console.error('Failed to delete item:', error);
        alert('Failed to delete item');
      }
    }
  }

  // Reactive statements
  let totalPages = $derived(getTotalPages());

  // Initialization and debounced handlers
  let searchTimeout: NodeJS.Timeout;
  let filterTimeout: NodeJS.Timeout;
  let hasLoaded = $state(false);

  // Single initialization effect
  $effect(() => {
    if (token && items && items.length > 0 && !hasLoaded) {
      hasLoaded = true;
      loadItems();
    }
  });

  // Search handler with debouncing
  function handleSearchInput() {
    if (searchTimeout) clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      if (searchQuery.length === 0 || searchQuery.length >= 3) {
        handleSearchChange();
      }
    }, 500);
  }

  // Filter handler with debouncing
  function handleStatusFilterInput() {
    if (filterTimeout) clearTimeout(filterTimeout);
    filterTimeout = setTimeout(() => {
      handleStatusFilterChange();
    }, 100);
  }
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Items Management</h2>
    <button
      onclick={() => showAddDialog = true}
      disabled={!users || users.length === 0}
      title={!users || users.length === 0 ? "Create a user first" : ""}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
    >
      Add New Item
    </button>
  </div>

  <!-- Search and Filter Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search items by name, slug, or URL (min 3 characters)..."
        bind:value={searchQuery}
        oninput={handleSearchInput}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="flex gap-2 items-center">
      <label for="item_status_filter" class="text-sm font-medium text-gray-700">Status:</label>
      <select
        id="item_status_filter"
        bind:value={statusFilter}
        onchange={handleStatusFilterInput}
        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="">All</option>
        <option value="0">Pending (0)</option>
        <option value="1">Active (1)</option>
        <option value="2">Featured (2)</option>
      </select>
    </div>
    <div class="text-sm text-gray-600">
      {loading ? 'Loading...' : `${displayedItems?.length || 0} of ${totalItems || 0} items`}
    </div>
  </div>

  {#if loading}
    <div class="flex justify-center items-center py-8">
      <div class="text-gray-500">Loading items...</div>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {#each displayedItems as item}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{item.item_name}</h3>
                <p class="text-sm text-gray-600">Slug: {item.item_slug}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {item.item_id}
              </span>
            </div>

            <div class="space-y-1">
              <p class="text-sm text-blue-600 break-all">{item.item_url || 'No URL'}</p>
              <div class="flex items-center gap-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {item.item_status === 1 ? 'bg-green-100 text-green-800' : item.item_status === 2 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                  {item.item_status === 0 ? 'Pending' : item.item_status === 1 ? 'Active' : 'Featured'}
                </span>
              </div>
              <p class="text-xs text-gray-500">User ID: {item.user_id}</p>
              <p class="text-xs text-gray-500">Created: {new Date(Number(item.item_created_at)).toLocaleDateString()}</p>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditItem(item)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteItem(item.item_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="flex justify-center items-center gap-4 mt-6">
      <button
        onclick={() => changePage(currentPage - 1)}
        disabled={currentPage === 1}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Previous
      </button>
      
      <span class="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>
      
      <button
        onclick={() => changePage(currentPage + 1)}
        disabled={currentPage === totalPages}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Next
      </button>
    </div>
  {/if}
{/if}
</div>

<!-- Add Item Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New Item</Dialog.Title>
        <Dialog.Description>Create a new item.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_name" class="text-right text-sm font-medium">Name</label>
          <input
            id="item_name"
            type="text"
            bind:value={newItem.item_name}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Item name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_slug" class="text-right text-sm font-medium">Slug</label>
          <input
            id="item_slug"
            type="text"
            bind:value={newItem.item_slug}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="item-slug"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_url" class="text-right text-sm font-medium">URL</label>
          <input
            id="item_url"
            type="text"
            bind:value={newItem.item_url}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_status" class="text-right text-sm font-medium">Status</label>
          <select
            id="item_status"
            bind:value={newItem.item_status}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>Pending</option>
            <option value={1}>Active</option>
            <option value={2}>Featured</option>
          </select>
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_user_id" class="text-right text-sm font-medium">User</label>
          <select
            id="item_user_id"
            bind:value={newItem.user_id}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select a user (required)</option>
            {#each users as user}
              <option value={user.user_id}>{user.user_firstname} ({user.user_email})</option>
            {/each}
          </select>
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addItem}
          disabled={!newItem.item_name || !newItem.item_slug || !newItem.user_id}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add Item
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit Item Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit Item</Dialog.Title>
        <Dialog.Description>Update item information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_name" class="text-right text-sm font-medium">Name</label>
          <input
            id="edit_item_name"
            type="text"
            bind:value={editItem.item_name}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Item name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_slug" class="text-right text-sm font-medium">Slug</label>
          <input
            id="edit_item_slug"
            type="text"
            bind:value={editItem.item_slug}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="item-slug"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_url" class="text-right text-sm font-medium">URL</label>
          <input
            id="edit_item_url"
            type="text"
            bind:value={editItem.item_url}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_status" class="text-right text-sm font-medium">Status</label>
          <select
            id="edit_item_status"
            bind:value={editItem.item_status}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>Pending</option>
            <option value={1}>Active</option>
            <option value={2}>Featured</option>
          </select>
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_user_id" class="text-right text-sm font-medium">User</label>
          <select
            id="edit_item_user_id"
            bind:value={editItem.user_id}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select a user (required)</option>
            {#each users as user}
              <option value={user.user_id}>{user.user_firstname} ({user.user_email})</option>
            {/each}
          </select>
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateItem}
          disabled={!editItem.item_name || !editItem.item_slug || !editItem.user_id}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update Item
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
