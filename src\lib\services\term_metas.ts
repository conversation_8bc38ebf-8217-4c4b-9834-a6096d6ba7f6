import sql from '$lib/db/db.js';
import type { TermMeta } from '$lib/types/tables.js';

export async function saveTermMeta(termMeta: TermMeta): Promise<TermMeta> {
  if (termMeta.term_meta_id) {
    const [updatedTermMeta] = await sql`
      UPDATE term_metas
      SET term_id = ${termMeta.term_id},
          term_meta_key = ${termMeta.term_meta_key},
          term_meta_value = ${termMeta.term_meta_value || null}
      WHERE term_meta_id = ${termMeta.term_meta_id}
      RETURNING *
    `;
    return updatedTermMeta as TermMeta;
  } else {
    const [newTermMeta] = await sql`
      INSERT INTO term_metas (term_id, term_meta_key, term_meta_value)
      VALUES (${termMeta.term_id}, ${termMeta.term_meta_key}, ${termMeta.term_meta_value || null})
      RETURNING *
    `;
    return newTermMeta as TermMeta;
  }
}

export async function deleteTermMeta(term_meta_id: number): Promise<boolean> {
  const result = await sql`
    DELETE FROM term_metas WHERE term_meta_id = ${term_meta_id}
  `;
  return result.count > 0;
}

export async function getTermMetas(term_id?: number, limit = 50, offset = 0): Promise<{ term_metas: TermMeta[]; total: number }> {
  let termMetas;
  let countResult;

  if (term_id !== undefined) {
    termMetas = await sql`
      SELECT * FROM term_metas
      WHERE term_id = ${term_id}
      ORDER BY term_meta_key
      LIMIT ${limit} OFFSET ${offset}
    `;

    [countResult] = await sql`
      SELECT COUNT(*) as count FROM term_metas WHERE term_id = ${term_id}
    `;
  } else {
    termMetas = await sql`
      SELECT * FROM term_metas
      ORDER BY term_meta_key
      LIMIT ${limit} OFFSET ${offset}
    `;

    [countResult] = await sql`
      SELECT COUNT(*) as count FROM term_metas
    `;
  }

  return { term_metas: termMetas as unknown as TermMeta[], total: Number(countResult.count) };
}

export async function searchTermMetas(query: string, limit = 50, offset = 0): Promise<{ term_metas: TermMeta[]; total: number }> {
  if (!query || query.trim() === '') {
    return { term_metas: [], total: 0 };
  }

  const searchTerm = `%${query.trim()}%`;

  const termMetas = await sql`
    SELECT * FROM term_metas
    WHERE term_meta_key ILIKE ${searchTerm}
       OR term_meta_value ILIKE ${searchTerm}
    ORDER BY term_meta_key
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM term_metas
    WHERE term_meta_key ILIKE ${searchTerm}
       OR term_meta_value ILIKE ${searchTerm}
  `;

  return { term_metas: termMetas as unknown as TermMeta[], total: Number(countResult.count) };
}

export async function getTermMetasByKey(meta_key: string, limit = 50, offset = 0): Promise<{ term_metas: TermMeta[]; total: number }> {
  if (!meta_key || meta_key.trim() === '') {
    return { term_metas: [], total: 0 };
  }

  const termMetas = await sql`
    SELECT * FROM term_metas
    WHERE term_meta_key = ${meta_key.trim()}
    ORDER BY term_meta_key
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM term_metas
    WHERE term_meta_key = ${meta_key.trim()}
  `;

  return { term_metas: termMetas as unknown as TermMeta[], total: Number(countResult.count) };
}

export async function getTermMetaById(term_meta_id: number): Promise<TermMeta | null> {
  const [termMeta] = await sql`
    SELECT * FROM term_metas WHERE term_meta_id = ${term_meta_id}
  `;
  return (termMeta as TermMeta) || null;
}