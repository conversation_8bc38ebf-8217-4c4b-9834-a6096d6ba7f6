import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { getUsers, searchUsers } from '$lib/services/users.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    // Validate and parse limit parameter
    let limit = 50; // default
    if (body.limit !== undefined) {
      if (typeof body.limit !== 'number' || body.limit <= 0 || body.limit > 1000) {
        return json(createErrorResponse(new Error('Invalid limit parameter. Must be between 1 and 1000.')), { status: 400 });
      }
      limit = body.limit;
    }

    // Validate and parse offset parameter
    let offset = 0; // default
    if (body.offset !== undefined) {
      if (typeof body.offset !== 'number' || body.offset < 0) {
        return json(createErrorResponse(new Error('Invalid offset parameter. Must be a non-negative integer.')), { status: 400 });
      }
      offset = body.offset;
    }

    const search = body.search || '';

    let result;

    // If there's a search query, use search function
    if (search && search.trim().length >= 3) {
      result = await searchUsers(search.trim(), limit, offset);
    }
    // Otherwise, use regular getUsers
    else {
      result = await getUsers(limit, offset);
    }

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
