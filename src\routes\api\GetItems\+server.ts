import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { getItems, getItemsByStatus, searchItems } from '$lib/services/items.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    // Use body parameters with defaults
    const user_id = body.user_id;
    const limit = body.limit || 50;
    const offset = body.offset || 0;
    const search = body.search || '';
    const status = body.status;

    let result;

    // If there's a search query, use search function (with optional status filter)
    if (search && search.trim().length >= 3) {
      const statusNum = (status !== undefined && status !== '') ? parseInt(status) : undefined;
      result = await searchItems(search.trim(), limit, offset, statusNum);
    }
    // If there's a status filter (but no search), use status-based function
    else if (status !== undefined && status !== '') {
      const statusNum = parseInt(status);
      result = await getItemsByStatus(statusNum, limit, offset);
    }
    // Otherwise, use regular getItems
    else {
      result = await getItems(user_id, limit, offset);
    }

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
