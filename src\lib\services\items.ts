import sql from '$lib/db/db.js';
import type { Item } from '$lib/types/tables.js';

export async function saveItem(item: Item): Promise<Item> {
  if (item.item_id) {
    const [updatedItem] = await sql`
      UPDATE items
      SET item_name = ${item.item_name},
          item_slug = ${item.item_slug},
          item_url = ${item.item_url},
          item_status = ${item.item_status},
          item_created_at = ${item.item_created_at},
          user_id = ${item.user_id}
      WHERE item_id = ${item.item_id}
      RETURNING *
    `;
    return updatedItem as Item;
  } else {
    const [newItem] = await sql`
      INSERT INTO items (item_name, item_slug, item_url, item_status, item_created_at, user_id)
      VALUES (${item.item_name}, ${item.item_slug}, ${item.item_url}, ${item.item_status}, ${item.item_created_at}, ${item.user_id})
      RETURNING *
    `;
    return newItem as Item;
  }
}

export async function deleteItem(item_id: number): Promise<boolean> {
  const result = await sql`
    DELETE FROM items WHERE item_id = ${item_id}
  `;
  return result.count > 0;
}

export async function getItems(user_id?: number, limit = 50, offset = 0): Promise<{ items: Item[]; total: number }> {
  let items;
  let countResult;

  if (user_id !== undefined) {
    items = await sql`
      SELECT * FROM items
      WHERE user_id = ${user_id}
      ORDER BY item_created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    [countResult] = await sql`
      SELECT COUNT(*) as count FROM items WHERE user_id = ${user_id}
    `;
  } else {
    items = await sql`
      SELECT * FROM items
      ORDER BY item_created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    [countResult] = await sql`
      SELECT COUNT(*) as count FROM items
    `;
  }

  return { items: items as unknown as Item[], total: Number(countResult.count) };
}

export async function getItemById(item_id: number): Promise<Item | null> {
  const [item] = await sql`
    SELECT * FROM items WHERE item_id = ${item_id}
  `;
  return (item as Item) || null;
}

export async function getItemBySlug(slug: string): Promise<Item | null> {
  const [item] = await sql`
    SELECT * FROM items WHERE item_slug = ${slug}
  `;
  return (item as Item) || null;
}

export async function getFeaturedItems(limit = 8, offset = 0): Promise<{ items: Item[]; total: number }> {
  const items = await sql`
    SELECT * FROM items
    WHERE item_status = 2
    ORDER BY item_created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM items WHERE item_status = 2
  `;

  return { items: items as unknown as Item[], total: Number(countResult.count) };
}

export async function getItemsByStatus(status: number, limit = 50, offset = 0): Promise<{ items: Item[]; total: number }> {
  const items = await sql`
    SELECT * FROM items
    WHERE item_status = ${status}
    ORDER BY item_created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM items WHERE item_status = ${status}
  `;

  return { items: items as unknown as Item[], total: Number(countResult.count) };
}

export async function getItemsByTerm(term_slug: string, term_taxonomy: string, limit = 50, offset = 0): Promise<{ items: Item[]; total: number }> {
  const items = await sql`
    SELECT DISTINCT i.* FROM items i
    JOIN item_term_relationships itr ON i.item_id = itr.item_id
    JOIN terms t ON itr.term_id = t.term_id
    WHERE t.term_slug = ${term_slug}
      AND t.term_taxonomy = ${term_taxonomy}
    ORDER BY i.item_created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(DISTINCT i.item_id) as count FROM items i
    JOIN item_term_relationships itr ON i.item_id = itr.item_id
    JOIN terms t ON itr.term_id = t.term_id
    WHERE t.term_slug = ${term_slug}
      AND t.term_taxonomy = ${term_taxonomy}
  `;

  return { items: items as unknown as Item[], total: Number(countResult.count) };
}

export async function getActiveItemsByTerm(term_slug: string, term_taxonomy: string, limit = 50, offset = 0): Promise<{ items: Item[]; total: number }> {
  const items = await sql`
    SELECT DISTINCT i.* FROM items i
    JOIN item_term_relationships itr ON i.item_id = itr.item_id
    JOIN terms t ON itr.term_id = t.term_id
    WHERE t.term_slug = ${term_slug}
      AND t.term_taxonomy = ${term_taxonomy}
      AND (i.item_status = 1 OR i.item_status = 2)
    ORDER BY i.item_created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(DISTINCT i.item_id) as count FROM items i
    JOIN item_term_relationships itr ON i.item_id = itr.item_id
    JOIN terms t ON itr.term_id = t.term_id
    WHERE t.term_slug = ${term_slug}
      AND t.term_taxonomy = ${term_taxonomy}
      AND (i.item_status = 1 OR i.item_status = 2)
  `;

  return { items: items as unknown as Item[], total: Number(countResult.count) };
}

// Unified function to get items with metadata and terms
export async function getItemsWithMetadata(
  status: 'active' | 'featured' | 'all' = 'active',
  limit = 50,
  offset = 0
): Promise<{ items: any[]; total: number }> {
  // Build WHERE clause based on status parameter
  let statusCondition = '';
  if (status === 'featured') {
    statusCondition = "AND i.item_status = 2";
  } else if (status === 'active') {
    statusCondition = "AND (i.item_status = 1 OR i.item_status = 2)";
  }
  // For 'all', no additional status condition is added

  // Single optimized query to get items with metadata and terms
  const results = await sql`
    SELECT
      i.*,
      im.item_meta_key,
      im.item_meta_value,
      t.term_id,
      t.term_name,
      t.term_slug,
      t.term_taxonomy
    FROM items i
    LEFT JOIN item_metas im ON i.item_id = im.item_id
    LEFT JOIN item_term_relationships itr ON i.item_id = itr.item_id
    LEFT JOIN terms t ON itr.term_id = t.term_id
    WHERE 1=1 ${sql.unsafe(statusCondition)}
    ORDER BY i.item_created_at DESC, i.item_id, im.item_meta_key, t.term_name
    LIMIT ${limit * 20} OFFSET ${offset * 20}
  `;

  // Group results by item_id
  const itemMap = new Map();

  for (const row of results) {
    if (!itemMap.has(row.item_id)) {
      itemMap.set(row.item_id, {
        item_id: row.item_id,
        item_name: row.item_name,
        item_slug: row.item_slug,
        item_status: row.item_status,
        item_url: row.item_url,
        item_created_at: row.item_created_at,
        user_id: row.user_id,
        metadata: [],
        terms: []
      });
    }

    const item = itemMap.get(row.item_id);

    // Add metadata if exists and not already added
    if (row.item_meta_key && !item.metadata.some((m: any) => m.item_meta_key === row.item_meta_key)) {
      item.metadata.push({
        item_meta_key: row.item_meta_key,
        item_meta_value: row.item_meta_value
      });
    }

    // Add term if exists and not already added
    if (row.term_id && !item.terms.some((t: any) => t.term_id === row.term_id)) {
      item.terms.push({
        term_id: row.term_id,
        term_name: row.term_name,
        term_slug: row.term_slug,
        term_taxonomy: row.term_taxonomy
      });
    }
  }

  // Convert to array and limit to requested number of items
  const items = Array.from(itemMap.values()).slice(0, limit);

  // Get total count with same status condition
  let countQuery;
  if (status === 'featured') {
    countQuery = sql`SELECT COUNT(*) as count FROM items WHERE item_status = 2`;
  } else if (status === 'active') {
    countQuery = sql`SELECT COUNT(*) as count FROM items WHERE (item_status = 1 OR item_status = 2)`;
  } else {
    countQuery = sql`SELECT COUNT(*) as count FROM items`;
  }

  const [countResult] = await countQuery;
  return { items, total: Number(countResult.count) };
}

export async function getFeaturedItemsWithMetadata(limit = 50, offset = 0): Promise<{ items: any[]; total: number }> {
  return getItemsWithMetadata('featured', limit, offset);
}

export async function getItemsWithBasicMetadata(limit = 50, offset = 0): Promise<{ items: any[]; total: number }> {
  // Get items first
  const items = await sql`
    SELECT * FROM items
    ORDER BY item_created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;

  // Get metadata for these items
  const itemIds = items.map((item: any) => item.item_id);

  let metadata: any[] = [];
  if (itemIds.length > 0) {
    metadata = await sql`
      SELECT item_id, item_meta_key, item_meta_value
      FROM item_metas
      WHERE item_id = ANY(${itemIds})
    `;
  }

  // Get term relationships for these items
  let termRelationships: any[] = [];
  if (itemIds.length > 0) {
    termRelationships = await sql`
      SELECT
        itr.item_id,
        t.term_id,
        t.term_name,
        t.term_slug,
        t.term_taxonomy
      FROM item_term_relationships itr
      JOIN terms t ON itr.term_id = t.term_id
      WHERE itr.item_id = ANY(${itemIds})
    `;
  }

  // Combine data
  const enrichedItems = items.map((item: any) => {
    const itemMetadata = metadata.filter((m: any) => m.item_id === item.item_id);
    const itemTerms = termRelationships.filter((tr: any) => tr.item_id === item.item_id);

    return {
      ...item,
      metadata: itemMetadata,
      terms: itemTerms
    };
  });

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM items
  `;

  return { items: enrichedItems, total: Number(countResult.count) };
}

export async function getActiveItemsWithBasicMetadata(limit = 50, offset = 0): Promise<{ items: any[]; total: number }> {
  return getItemsWithMetadata('active', limit, offset);
}

export async function getActiveItemsByTermWithMetadata(term_slug: string, term_taxonomy: string, limit = 50, offset = 0): Promise<{ items: any[]; total: number }> {
  // Get items first (only active or featured)
  const items = await sql`
    SELECT DISTINCT i.* FROM items i
    JOIN item_term_relationships itr ON i.item_id = itr.item_id
    JOIN terms t ON itr.term_id = t.term_id
    WHERE t.term_slug = ${term_slug}
      AND t.term_taxonomy = ${term_taxonomy}
      AND (i.item_status = 1 OR i.item_status = 2)
    ORDER BY i.item_created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;

  // Get metadata for these items
  const itemIds = items.map((item: any) => item.item_id);

  let metadata: any[] = [];
  if (itemIds.length > 0) {
    metadata = await sql`
      SELECT item_id, item_meta_key, item_meta_value
      FROM item_metas
      WHERE item_id = ANY(${itemIds})
    `;
  }

  // Get term relationships for these items
  let termRelationships: any[] = [];
  if (itemIds.length > 0) {
    termRelationships = await sql`
      SELECT
        itr.item_id,
        t.term_id,
        t.term_name,
        t.term_slug,
        t.term_taxonomy
      FROM item_term_relationships itr
      JOIN terms t ON itr.term_id = t.term_id
      WHERE itr.item_id = ANY(${itemIds})
    `;
  }

  // Combine data
  const enrichedItems = items.map((item: any) => {
    const itemMetadata = metadata.filter((m: any) => m.item_id === item.item_id);
    const itemTerms = termRelationships.filter((tr: any) => tr.item_id === item.item_id);

    return {
      ...item,
      metadata: itemMetadata,
      terms: itemTerms
    };
  });

  const [countResult] = await sql`
    SELECT COUNT(DISTINCT i.item_id) as count FROM items i
    JOIN item_term_relationships itr ON i.item_id = itr.item_id
    JOIN terms t ON itr.term_id = t.term_id
    WHERE t.term_slug = ${term_slug}
      AND t.term_taxonomy = ${term_taxonomy}
      AND (i.item_status = 1 OR i.item_status = 2)
  `;

  return { items: enrichedItems, total: Number(countResult.count) };
}

export async function searchItems(query: string, limit = 50, offset = 0, status?: number): Promise<{ items: any[]; total: number }> {
  if (!query || query.trim() === '') {
    return { items: [], total: 0 };
  }

  const searchTerm = `%${query.trim()}%`;

  // Single optimized query to get items with metadata and terms that match the search
  const results = await sql`
    SELECT DISTINCT
      i.*,
      im.item_meta_key,
      im.item_meta_value,
      t.term_id,
      t.term_name,
      t.term_slug,
      t.term_taxonomy
    FROM items i
    LEFT JOIN item_metas im ON i.item_id = im.item_id
    LEFT JOIN item_term_relationships itr ON i.item_id = itr.item_id
    LEFT JOIN terms t ON itr.term_id = t.term_id
    WHERE ${status !== undefined ? sql`i.item_status = ${status}` : sql`1=1`}
      AND (
        i.item_name ILIKE ${searchTerm}
        OR i.item_slug ILIKE ${searchTerm}
        OR i.item_url ILIKE ${searchTerm}
      )
    ORDER BY i.item_created_at DESC, i.item_id, im.item_meta_key, t.term_name
    LIMIT ${limit * 20} OFFSET ${offset * 20}
  `;

  // Group results by item_id (same logic as other functions)
  const itemMap = new Map();

  for (const row of results) {
    if (!itemMap.has(row.item_id)) {
      itemMap.set(row.item_id, {
        ...row,
        metadata: [],
        terms: []
      });
    }

    const item = itemMap.get(row.item_id);

    // Add metadata if exists and not already added
    if (row.item_meta_key && !item.metadata.some((m: any) => m.item_meta_key === row.item_meta_key)) {
      item.metadata.push({
        item_meta_key: row.item_meta_key,
        item_meta_value: row.item_meta_value
      });
    }

    // Add term if exists and not already added
    if (row.term_id && !item.terms.some((t: any) => t.term_id === row.term_id)) {
      item.terms.push({
        term_id: row.term_id,
        term_name: row.term_name,
        term_slug: row.term_slug,
        term_taxonomy: row.term_taxonomy
      });
    }
  }

  // Convert to array and limit to requested number of items
  const items = Array.from(itemMap.values()).slice(0, limit);

  // Get total count for search results
  const [countResult] = await sql`
    SELECT COUNT(DISTINCT i.item_id) as count
    FROM items i
    WHERE ${status !== undefined ? sql`i.item_status = ${status}` : sql`1=1`}
      AND (
        i.item_name ILIKE ${searchTerm}
        OR i.item_slug ILIKE ${searchTerm}
        OR i.item_url ILIKE ${searchTerm}
      )
  `;

  return { items, total: Number(countResult.count) };
}