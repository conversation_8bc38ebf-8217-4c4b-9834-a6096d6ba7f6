import sql from '$lib/db/db.js';
import type { Option } from '$lib/types/tables.js';

export async function saveOption(option: Option): Promise<Option> {
  if (option.option_id) {
    const [updatedOption] = await sql`
      UPDATE options
      SET option_key = ${option.option_key},
          option_value = ${option.option_value || null}
      WHERE option_id = ${option.option_id}
      RETURNING *
    `;
    return updatedOption as Option;
  } else {
    const [newOption] = await sql`
      INSERT INTO options (option_key, option_value)
      VALUES (${option.option_key}, ${option.option_value || null})
      RETURNING *
    `;
    return newOption as Option;
  }
}

export async function deleteOption(option_id: number): Promise<boolean> {
  const result = await sql`
    DELETE FROM options WHERE option_id = ${option_id}
  `;
  return result.count > 0;
}

export async function getOptions(limit = 50, offset = 0): Promise<{ options: Option[]; total: number }> {
  const options = await sql`
    SELECT * FROM options
    ORDER BY option_key
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM options
  `;

  return { options: options as unknown as Option[], total: Number(countResult.count) };
}

export async function searchOptions(query: string, limit = 50, offset = 0): Promise<{ options: Option[]; total: number }> {
  if (!query || query.trim() === '') {
    return { options: [], total: 0 };
  }

  const searchTerm = `%${query.trim()}%`;

  const options = await sql`
    SELECT * FROM options
    WHERE option_key ILIKE ${searchTerm}
       OR option_value ILIKE ${searchTerm}
    ORDER BY option_key
    LIMIT ${limit} OFFSET ${offset}
  `;

  const [countResult] = await sql`
    SELECT COUNT(*) as count FROM options
    WHERE option_key ILIKE ${searchTerm}
       OR option_value ILIKE ${searchTerm}
  `;

  return { options: options as unknown as Option[], total: Number(countResult.count) };
}

export async function getOptionById(option_id: number): Promise<Option | null> {
  const [option] = await sql`
    SELECT * FROM options WHERE option_id = ${option_id}
  `;
  return (option as Option) || null;
}